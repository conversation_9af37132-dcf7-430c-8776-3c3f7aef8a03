import { useUIStore } from "@/stores/uiStore";
import { useEffect, useCallback, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
	Plus,
	Settings,
	RefreshCcw,
	MessageCircleMore,
	Trash2,
	Pencil,
	Info,
	Search,
} from "lucide-react";
import { Input } from "@/components/ui/input";
import { useDebounce } from "@/hooks/useDebounce";
import { EmptyContent } from "@/components/ui-components/EmptyContent";
import { type Category } from "@/components/ui-components/CategoryListCard";
import { CategoryDetailsSheet } from "@/components/dashboard/category/CategoryDetailsSheet";
import {
	AddCategorySheet,
	type CategoryFormData,
} from "@/components/dashboard/category/AddCategorySheet";
import { EditCategorySheet } from "@/components/dashboard/category/EditCategorySheet";
import { DeleteConfirmationDialog } from "@/components/common/DeleteConfirmationDialog";
import {
	Toolt<PERSON>,
	TooltipContent,
	TooltipTrigger,
} from "@/components/ui/tooltip";
import {
	Pagination,
	PaginationContent,
	PaginationEllipsis,
	PaginationItem,
	PaginationLink,
	PaginationNext,
	PaginationPrevious,
} from "@/components/ui/pagination";
import {
	useCategories,
	useDeleteCategory,
	useCategoryDetail,
} from "@/hooks/useCategories";
import { useQueryClient } from "@tanstack/react-query";
import { queryKeys } from "@/lib/query";
import { transformCategoriesDataToCategories } from "@/lib/utils/categoryTransformers";
import { useOrganizationContext } from "@/features/organizations/context/OrganizationContext";

export default function PatientCategories() {
	const setBreadcrumbs = useUIStore((state) => state.setBreadcrumbs);
	const setPageHeaderContent = useUIStore(
		(state) => state.setPageHeaderContent
	);
	const queryClient = useQueryClient();
	const { organizationId } = useOrganizationContext();
	const [currentPage, setCurrentPage] = useState(1);
	const [searchQuery, setSearchQuery] = useState("");
	const categoriesPerPage = 10;
	const debouncedSearchTerm = useDebounce(searchQuery, 300);

	const {
		data: categoriesData,
		isLoading,
		error,
		refetch,
	} = useCategories({
		page: currentPage,
		limit: categoriesPerPage,
		search: debouncedSearchTerm || undefined,
	});

	const categories: Category[] = categoriesData?.data
		? transformCategoriesDataToCategories(categoriesData.data)
		: [];
	const totalPages = categoriesData?.meta?.pagination?.total_pages || 1;
	const hasCategories = categories.length > 0;
	useEffect(() => {
		if (categoriesData?.meta?.pagination) {
			const totalItems = categoriesData.meta.pagination.total;
			const itemsPerPage = categoriesPerPage;
			const maxPage = Math.ceil(totalItems / itemsPerPage);
			if (currentPage > maxPage && maxPage > 0) {
				setCurrentPage(maxPage);
			}
		}
	}, [
		categoriesData?.meta?.pagination?.total,
		currentPage,
		categoriesPerPage,
	]);

	const [isCategoryDetailsOpen, setIsCategoryDetailsOpen] = useState(false);
	const [selectedCategoryForDetails, setSelectedCategoryForDetails] =
		useState<Category | null>(null);
	const [isAddCategoryOpen, setIsAddCategoryOpen] = useState(false);
	const [isEditCategoryOpen, setIsEditCategoryOpen] = useState(false);
	const [selectedCategoryIdForEdit, setSelectedCategoryIdForEdit] = useState<
		string | null
	>(null);
	const [isRefreshing, setIsRefreshing] = useState(false);
	const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
	const [categoryToDelete, setCategoryToDelete] = useState<Category | null>(
		null
	);

	const { data: categoryDetailData } = useCategoryDetail(
		selectedCategoryIdForEdit || "",
		{
			enabled: !!selectedCategoryIdForEdit && isEditCategoryOpen,
		}
	);

	const currentCategories = categories;

	const deleteCategoryMutation = useDeleteCategory({
		onSuccess: () => {
			setIsDeleteDialogOpen(false);
			setCategoryToDelete(null);
			if (selectedCategoryForDetails?.id === categoryToDelete?.id) {
				setIsCategoryDetailsOpen(false);
				setSelectedCategoryForDetails(null);
			}
		},
		onError: (error) => {
			console.error("Failed to delete category:", error);
		},
	});

	const handleAddCategory = useCallback(() => {
		setIsAddCategoryOpen(true);
	}, []);

	const handleAddCategorySubmit = useCallback(
		(data: CategoryFormData) => {
			setIsAddCategoryOpen(false);
			refetch();
		},
		[refetch]
	);

	const handleEditCategorySubmit = useCallback(
		(data: CategoryFormData) => {
			setIsEditCategoryOpen(false);
			setSelectedCategoryIdForEdit(null);
			refetch();
		},
		[refetch]
	);

	const handleOpenCategoryDetails = useCallback((category: Category) => {
		setSelectedCategoryForDetails(category);
		setIsCategoryDetailsOpen(true);
	}, []);

	const handleCloseCategoryDetails = useCallback(() => {
		setIsCategoryDetailsOpen(false);
		setSelectedCategoryForDetails(null);
		console.log("Closed category details");
	}, []);

	const handleSettings = useCallback(() => {
		console.log("Settings clicked");
	}, []);

	const handleRefresh = useCallback(async () => {
		setIsRefreshing(true);
		try {
			await queryClient.invalidateQueries({
				queryKey: queryKeys.categories.lists(),
			});
			const result = await refetch();
		} catch (error) {
			console.error("Refresh failed:", error);
		} finally {
			setIsRefreshing(false);
		}
	}, [
		refetch,
		queryClient,
		organizationId,
		currentPage,
		categoriesPerPage,
		debouncedSearchTerm,
	]);

	const handlePageChange = useCallback((page: number) => {
		setCurrentPage(page);
	}, []);

	const handlePreviousPage = useCallback(() => {
		if (currentPage > 1) {
			handlePageChange(currentPage - 1);
		}
	}, [currentPage, handlePageChange]);

	const handleNextPage = useCallback(() => {
		if (currentPage < totalPages) {
			handlePageChange(currentPage + 1);
		}
	}, [currentPage, totalPages, handlePageChange]);

	const handleEditCategory = useCallback((category: Category) => {
		setSelectedCategoryIdForEdit(category.id);
		setIsEditCategoryOpen(true);
	}, []);

	const handleDeleteCategory = useCallback((category: Category) => {
		setCategoryToDelete(category);
		setIsDeleteDialogOpen(true);
	}, []);

	const handleConfirmDelete = useCallback(() => {
		if (categoryToDelete) {
			deleteCategoryMutation.mutate(categoryToDelete.id);
		}
	}, [categoryToDelete, deleteCategoryMutation]);

	const handleCancelDelete = useCallback(() => {
		setIsDeleteDialogOpen(false);
		setCategoryToDelete(null);
	}, []);

	const handleViewCategory = useCallback((category: Category) => {
		console.log("View category:", category.id);
	}, []);

	const handleInfoCategory = useCallback((category: Category) => {
		setSelectedCategoryForDetails(category);
		setIsCategoryDetailsOpen(true);
	}, []);

	useEffect(() => {
		if (debouncedSearchTerm !== undefined) {
			setCurrentPage(1);
		}
	}, [debouncedSearchTerm]);

	useEffect(() => {
		setBreadcrumbs([
			{ label: "Dashboard", href: "/" },
			{ label: "Patients", href: "/dashboard/patients" },
			{
				label: "Patient Categories",
				href: "/dashboard/patients/categories",
			},
		]);

		return () => {
			setBreadcrumbs([]);
		};
	}, [setBreadcrumbs]);

	useEffect(() => {
		const headerContent = (
			<div className="flex flex-1 items-center justify-between">
				<div>
					<h1 className="text-foreground text-2xl font-bold">
						List of Categories
					</h1>
				</div>
				<div className="flex items-center space-x-3">
					<div className="relative max-w-md flex-1">
						<Search className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform text-gray-400" />
						<Input
							placeholder="Search by name..."
							value={searchQuery}
							onChange={(e) => setSearchQuery(e.target.value)}
							className="pl-10"
							id="search-field"
						/>
					</div>
					<Button
						variant="outline"
						size="icon"
						onClick={handleSettings}
						className="h-9 w-9"
					>
						<Settings className="h-4 w-4" />
					</Button>
					<Button
						variant="outline"
						size="icon"
						className="h-9 w-9"
						onClick={handleRefresh}
						disabled={isRefreshing}
					>
						<RefreshCcw
							className={`h-4 w-4 ${isRefreshing ? "animate-spin" : ""}`}
						/>
					</Button>
					<Button
						variant="default"
						onClick={handleAddCategory}
						className="h-9 bg-[#005893] px-4 text-white hover:bg-[#004a7a]"
					>
						<Plus className="h-4 w-4" />
						Add Category
					</Button>
				</div>
			</div>
		);

		setPageHeaderContent(headerContent);

		return () => {
			setPageHeaderContent(null);
		};
	}, [
		setPageHeaderContent,
		searchQuery,
		handleSettings,
		handleRefresh,
		handleAddCategory,
		isLoading,
	]);

	if (isLoading) {
		return (
			<div className="flex min-h-[400px] items-center justify-center">
				<div className="text-center">
					<div className="mx-auto mb-4 h-8 w-8 animate-spin rounded-full border-b-2 border-[#005893]"></div>
					<p className="text-gray-500">Loading categories...</p>
				</div>
			</div>
		);
	}

	if (error) {
		return (
			<div className="flex min-h-[400px] items-center justify-center">
				<div className="text-center">
					<p className="mb-4 text-red-500">
						Failed to load categories
					</p>
					<Button onClick={() => refetch()} variant="outline">
						Try Again
					</Button>
				</div>
			</div>
		);
	}

	return (
		<div className="space-y-">
			{!hasCategories ? (
				<div className="flex min-h-[400px] items-center justify-center">
					<EmptyContent
						title="No categories added"
						description="You haven't added any categories yet. Add one to get started."
						actions={[
							{
								label: "Add New Categories",
								onClick: handleAddCategory,
								variant: "primary",
							},
						]}
					/>
				</div>
			) : (
				<div>
					<div className="mt-2 overflow-hidden rounded-xl border border-[#E4E4E7]">
						<table className="w-full">
							<thead>
								<tr className="h-12 border-b border-gray-200">
									<th className="w-14 px-3 text-left">
										<div className="text-xs font-medium text-[#71717A]">
											Color
										</div>
									</th>
									<th
										className="px-3 text-left"
										style={{ width: "auto" }}
									>
										<div className="text-xs font-medium text-[#71717A]">
											Category Name
										</div>
									</th>
									<th className="w-28 px-3 text-left">
										<div className="text-xs font-medium text-[#71717A]">
											Status
										</div>
									</th>
									<th className="w-44 px-3 text-left">
										<div className="text-xs font-medium text-[#71717A]">
											Clients
										</div>
									</th>
									<th className="w-44 px-3 text-left">
										<div className="text-xs font-medium text-[#71717A]">
											Stations
										</div>
									</th>
									<th className="w-48 px-3 text-left">
										<div className="text-xs font-medium text-[#71717A]">
											Date Added
										</div>
									</th>
								</tr>
							</thead>
							<tbody>
								{currentCategories.map((category) => (
									<tr
										key={category.id}
										onClick={() =>
											handleOpenCategoryDetails(category)
										}
										className="h-16 cursor-pointer border-t border-gray-200 bg-white transition-colors hover:bg-gray-50"
									>
										<td className="w-14 px-3">
											<div className="flex items-center justify-start">
												<div
													className="h-3 w-3 rounded-full"
													style={{
														backgroundColor:
															category.color,
													}}
												/>
											</div>
										</td>
										<td
											className="px-3"
											style={{ width: "auto" }}
										>
											<div className="text-sm leading-tight font-normal text-gray-900">
												{category.name}
											</div>
										</td>
										<td className="w-28 px-3">
											<div
												className={`inline-flex items-center justify-center gap-2.5 rounded-md px-2 py-1 ${
													category.status === "Active"
														? "bg-green-100 text-green-800"
														: "bg-gray-100 text-gray-600"
												}`}
											>
												<div className="text-[10px] leading-3 font-medium">
													{category.status}
												</div>
											</div>
										</td>
										<td className="w-44 px-3">
											<div className="inline-flex items-center justify-center gap-2.5 rounded-md bg-gray-100 px-2 py-1">
												<div className="text-[10px] leading-3 font-medium text-gray-900">
													{category.clients}
												</div>
											</div>
										</td>
										<td className="w-44 px-3">
											<div className="inline-flex items-center justify-center gap-2.5 rounded-md bg-gray-100 px-2 py-1">
												<div className="text-[10px] leading-3 font-medium text-gray-900">
													{category.stations}
												</div>
											</div>
										</td>
										<td className="w-48 px-3">
											<div className="text-xs leading-none font-normal text-gray-500">
												{category.dateAdded}
											</div>
										</td>
										<td className="w-16 px-3">
											<div className="flex items-center justify-end gap-1.5">
												<Tooltip>
													<TooltipTrigger asChild>
														<button
															onClick={(e) => {
																e.stopPropagation();
																handleViewCategory(
																	category
																);
															}}
															className="flex h-6 w-6 items-center justify-center rounded-md border border-gray-200 bg-gray-50 p-1 transition-colors hover:bg-gray-100"
														>
															<MessageCircleMore className="h-3 w-3 text-gray-500" />
														</button>
													</TooltipTrigger>
													<TooltipContent
														side="top"
														sideOffset={8}
														className="border-0 bg-white p-0 shadow-[0px_2px_4px_-1px_rgba(0,0,0,0.06)] [&_svg]:!hidden [&>*:last-child]:!hidden [&>svg]:!hidden"
													>
														<div className="relative">
															<div className="max-w-44 rounded-md bg-white px-2 py-1.5">
																<div className="text-xs leading-none font-normal text-gray-900">
																	View Details
																</div>
															</div>
															<div className="absolute top-full right-4">
																<div
																	className="h-1.5 w-3 bg-white"
																	style={{
																		clipPath:
																			"polygon(50% 100%, 0% 0%, 100% 0%)",
																	}}
																/>
															</div>
														</div>
													</TooltipContent>
												</Tooltip>
												<Tooltip>
													<TooltipTrigger asChild>
														<button
															onClick={(e) => {
																e.stopPropagation();
																handleDeleteCategory(
																	category
																);
															}}
															className="flex h-6 w-6 items-center justify-center rounded-md border border-gray-200 bg-gray-50 p-1 transition-colors hover:bg-gray-100 disabled:cursor-not-allowed disabled:opacity-50"
														>
															<Trash2 className="h-3 w-3 text-gray-500" />
														</button>
													</TooltipTrigger>
													<TooltipContent
														side="top"
														sideOffset={8}
														className="border-0 bg-white p-0 shadow-[0px_2px_4px_-1px_rgba(0,0,0,0.06)] [&_svg]:!hidden [&>*:last-child]:!hidden [&>svg]:!hidden"
													>
														<div className="relative">
															<div className="max-w-44 rounded-md bg-white px-2 py-1.5">
																<div className="text-xs leading-none font-normal text-gray-900">
																	Delete
																</div>
															</div>
															<div className="absolute top-full right-4">
																<div
																	className="h-1.5 w-3 bg-white"
																	style={{
																		clipPath:
																			"polygon(50% 100%, 0% 0%, 100% 0%)",
																	}}
																/>
															</div>
														</div>
													</TooltipContent>
												</Tooltip>
												<Tooltip>
													<TooltipTrigger asChild>
														<button
															onClick={(e) => {
																e.stopPropagation();
																handleEditCategory(
																	category
																);
															}}
															className="flex h-6 w-6 items-center justify-center rounded-md border border-gray-200 bg-gray-50 p-1 transition-colors hover:bg-gray-100"
														>
															<Pencil className="h-3 w-3 text-gray-500" />
														</button>
													</TooltipTrigger>
													<TooltipContent
														side="top"
														sideOffset={8}
														className="border-0 bg-white p-0 shadow-[0px_2px_4px_-1px_rgba(0,0,0,0.06)] [&_svg]:!hidden [&>*:last-child]:!hidden [&>svg]:!hidden"
													>
														<div className="relative">
															<div className="max-w-44 rounded-md bg-white px-2 py-1.5">
																<div className="text-xs leading-none font-normal text-gray-900">
																	Edit
																</div>
															</div>
															<div className="absolute top-full right-4">
																<div
																	className="h-1.5 w-3 bg-white"
																	style={{
																		clipPath:
																			"polygon(50% 100%, 0% 0%, 100% 0%)",
																	}}
																/>
															</div>
														</div>
													</TooltipContent>
												</Tooltip>
												<Tooltip>
													<TooltipTrigger asChild>
														<button
															onClick={(e) => {
																e.stopPropagation();
																handleInfoCategory(
																	category
																);
															}}
															className="flex h-6 w-6 items-center justify-center rounded-md border border-gray-200 bg-gray-50 p-1 transition-colors hover:bg-gray-100"
														>
															<Info className="h-3 w-3 text-gray-500" />
														</button>
													</TooltipTrigger>
													<TooltipContent
														side="top"
														sideOffset={8}
														className="border-0 bg-white p-0 shadow-[0px_2px_4px_-1px_rgba(0,0,0,0.06)] [&_svg]:!hidden [&>*:last-child]:!hidden [&>svg]:!hidden"
													>
														<div className="relative">
															<div className="max-w-44 rounded-md bg-white px-2 py-1.5">
																<div className="text-xs leading-none font-normal text-gray-900">
																	Information
																</div>
															</div>
															<div className="absolute top-full right-4">
																<div
																	className="h-1.5 w-3 bg-white"
																	style={{
																		clipPath:
																			"polygon(50% 100%, 0% 0%, 100% 0%)",
																	}}
																/>
															</div>
														</div>
													</TooltipContent>
												</Tooltip>
											</div>
										</td>
									</tr>
								))}
							</tbody>
						</table>
					</div>
					{totalPages > 1 && (
						<div className="mt-4 flex justify-end">
							<div>
								<Pagination>
									<PaginationContent>
										<PaginationItem>
											<PaginationPrevious
												onClick={handlePreviousPage}
												className={
													currentPage === 1
														? "pointer-events-none opacity-50"
														: "cursor-pointer"
												}
											/>
										</PaginationItem>
										{Array.from(
											{ length: totalPages },
											(_, i) => i + 1
										).map((page) => (
											<PaginationItem key={page}>
												<PaginationLink
													onClick={() =>
														handlePageChange(page)
													}
													isActive={
														currentPage === page
													}
													className="cursor-pointer"
												>
													{page}
												</PaginationLink>
											</PaginationItem>
										))}

										{totalPages > 5 &&
											currentPage < totalPages - 2 && (
												<PaginationItem>
													<PaginationEllipsis />
												</PaginationItem>
											)}

										<PaginationItem>
											<PaginationNext
												onClick={handleNextPage}
												className={
													currentPage === totalPages
														? "pointer-events-none opacity-50"
														: "cursor-pointer"
												}
											/>
										</PaginationItem>
									</PaginationContent>
								</Pagination>
							</div>
						</div>
					)}
				</div>
			)}
			<CategoryDetailsSheet
				open={isCategoryDetailsOpen}
				onClose={handleCloseCategoryDetails}
				category={selectedCategoryForDetails}
				onEdit={handleEditCategory}
			/>
			<AddCategorySheet
				open={isAddCategoryOpen}
				onOpenChange={setIsAddCategoryOpen}
				onSubmit={handleAddCategorySubmit}
			/>
			<EditCategorySheet
				open={isEditCategoryOpen}
				onOpenChange={setIsEditCategoryOpen}
				category={categoryDetailData?.data}
				onSubmit={handleEditCategorySubmit}
			/>
			<DeleteConfirmationDialog
				open={isDeleteDialogOpen}
				onOpenChange={setIsDeleteDialogOpen}
				title="Are you sure you want to delete this category?"
				description={`This action cannot be undone and will permanently delete ${categoryToDelete?.name || "this category"} and all its information.`}
				onConfirm={handleConfirmDelete}
				onCancel={handleCancelDelete}
				confirmText="Delete Category"
				isLoading={deleteCategoryMutation.isPending}
			/>
		</div>
	);
}
